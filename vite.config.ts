import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import UnoCSS from 'unocss/vite';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
// https://vite.dev/config/

const dir = (dir: string) => path.resolve(__dirname, dir);

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  console.log(env);
  return {
    base: env.VITE_PUBLISH_PATH,
    plugins: [
      vue(),
      AutoImport({
        dts: 'typings/auto-import.d.ts',
        imports: ['vue', 'vue-router'],
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        dts: 'typings/components.d.ts',
        resolvers: [ElementPlusResolver()]
      }),
      UnoCSS()
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/index.scss";`
        }
      }
    },
    resolve: {
      alias: {
        '@': dir('./src')
      }
    }
  };
});
