import { createApp } from 'vue';
import { createPinia } from 'pinia';
import 'virtual:uno.css';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import router from './router';
import './style.scss';
import App from './App.vue';
import { pluginsInstall } from './plugins';

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
pluginsInstall(app);

app.use(pinia).use(router).mount('#app');
