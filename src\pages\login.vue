<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/userStore';

const router = useRouter();
const userStore = useUserStore();

// 表单数据
const formData = ref({
  username: '',
  password: ''
});

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 16, message: '长度在 3 到 16 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 18, message: '长度在 6 到 18 个字符', trigger: 'blur' }
  ]
};

// 登录提交
const handleLogin = () => {
  // TODO: 实际登录接口调用
  ElMessage.success('登录成功！');
  console.log('登录数据:', formData.value);
  router.push('/');
};
</script>

<template>
  <div
    class="min-h-screen w100vw flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100"
  >
    <div class="w-full max-w-md">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">后台管理系统</h1>
        <p class="text-gray-500 mt-2">请输入您的账号和密码登录系统</p>
      </div>

      <el-card shadow="always" class="!border-0 rounded-xl">
        <el-form :model="formData" :rules="rules" label-position="top" class="p-6">
          <el-form-item label="账号" prop="username">
            <el-input v-model="formData.username" placeholder="请输入账号" size="large" clearable>
              <template #prefix>
                <div class="i-carbon-user text-gray-400"></div>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              show-password
            >
              <template #prefix>
                <div class="i-carbon-password text-gray-400"></div>
              </template>
            </el-input>
          </el-form-item>

          <!-- <div class="flex justify-between mb-6">
            <el-checkbox>记住密码</el-checkbox>
            <el-link type="primary" :underline="false">忘记密码?</el-link>
          </div> -->

          <el-button type="primary" size="large" class="w-full mt-24px" @click="handleLogin">
            登录
          </el-button>
        </el-form>
      </el-card>

      <div class="mt-6 text-center text-gray-500 text-sm">© 2023 后台管理系统 | 技术支持</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.el-card {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}
</style>
