{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.9.11", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.24", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "prettier": "^3.5.3", "sass": "^1.89.0", "typescript": "~5.8.3", "unocss": "^66.1.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}